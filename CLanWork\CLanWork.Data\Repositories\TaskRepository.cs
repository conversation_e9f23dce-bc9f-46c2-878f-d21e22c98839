using CLanWork.Data.Interfaces;
using Microsoft.EntityFrameworkCore;
using EntityTask = CLanWork.Data.Entities.Task;

namespace CLanWork.Data.Repositories
{
    public class TaskRepository : ITaskRepository
    {
        private readonly CLanWorkContext _context;

        public TaskRepository(CLanWorkContext context)
        {
            _context = context;
        }

        public async Task<EntityTask> CreateAsync(EntityTask task)
        {
            int maxId = await _context.Tasks.AnyAsync()
                ? await _context.Tasks.MaxAsync(t => t.TaskId)
                : 0;

            task.TaskId = maxId + 1;
            _context.Tasks.Add(task);
            await _context.SaveChangesAsync();
            return task;
        }

        public async Task DeleteAsync(int id)
        {
            var task = await _context.Tasks.FindAsync(id);

            if (task != null)
            {
                _context.Tasks.Remove(task);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<List<EntityTask>> GetAllAsync()
        {
            return await _context.Tasks
                .Include(t => t.Club)
                .ToListAsync();
        }

        public async Task<List<EntityTask>> GetByClubIdAsync(int clubId)
        {
            return await _context.Tasks
                .Where(t => t.ClubId == clubId)
                .Include(t => t.Assignees).ThenInclude(a => a.User)
                .Include(t => t.Comments)
                .Include(t => t.Proofs)
                .ToListAsync();
        }

        public async Task<EntityTask?> GetByIdAsync(int id)
        {
            return await _context.Tasks
                .Include(t => t.Club)
                .Include(t => t.Assignees).ThenInclude(a => a.User)
                .Include(t => t.Comments).ThenInclude(c => c.User)
                .Include(t => t.Proofs)
                .FirstOrDefaultAsync(t => t.TaskId == id);
        }

        public async Task<List<EntityTask>> GetUpcomingTasksAsync(DateTime from, DateTime to)
        {
            return await _context.Tasks
                .Where(t => t.DueDate.HasValue && t.DueDate >= from && t.DueDate <= to)
                .OrderBy(t => t.DueDate)
                .ToListAsync();
        }

        public async Task UpdateAsync(EntityTask task)
        {
            _context.Tasks.Update(task);
            await _context.SaveChangesAsync();
        }
    }
}