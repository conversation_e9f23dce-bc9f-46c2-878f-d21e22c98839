using CLanWork.Data.Entities;
using CLanWork.Data.Interfaces;
using Microsoft.EntityFrameworkCore;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Data.Repositories
{
    public class MemberRepository : IMemberRepository
    {
        private readonly CLanWorkContext _context;

        public MemberRepository(CLanWorkContext context)
        {
            _context = context;
        }

        public async Task<Member> CreateAsync(Member member)
        {
            int maxId = await _context.Members.AnyAsync()
                ? await _context.Members.MaxAsync(m => m.MemberId)
                : 0;

            member.MemberId = maxId + 1;
            _context.Members.Add(member);
            await _context.SaveChangesAsync();
            return member;
        }

        public async Task DeleteAsync(int id)
        {
            var member = await _context.Members.FindAsync(id);

            if (member != null)
            {
                _context.Members.Remove(member);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<List<Member>> GetAllAsync()
        {
            return await _context.Members
                .Include(m => m.User)
                .Include(m => m.Club)
                .ToListAsync();
        }

        public async Task<List<Member>> GetByClubIdAsync(int clubId)
        {
            return await _context.Members
                .Where(m => m.ClubId == clubId)
                .Include(m => m.User)
                .ToListAsync();
        }

        public async Task<Member?> GetByIdAsync(int id)
        {
            return await _context.Members
                .Include(m => m.User)
                .Include(m => m.Club)
                .FirstOrDefaultAsync(m => m.MemberId == id);
        }

        public async Task<List<Member>> GetByUserIdAsync(int userId)
        {
            return await _context.Members
                .Where(m => m.UserId == userId)
                .Include(m => m.Club)
                .ToListAsync();
        }

        public async Task UpdateAsync(Member member)
        {
            _context.Members.Update(member);
            await _context.SaveChangesAsync();
        }
    }
}