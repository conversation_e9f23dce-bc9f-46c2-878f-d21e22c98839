using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Web.Pages.TaskAssignees
{
    public class EditModel : PageModel
    {
        private readonly ITaskAssigneeService _taskAssigneeService;
        private readonly IUserService _userService;
        private readonly ITaskService _taskService;

        public EditModel(
            ITaskAssigneeService taskAssigneeService,
            IUserService userService,
            ITaskService taskService)
        {
            _taskAssigneeService = taskAssigneeService;
            _userService = userService;
            _taskService = taskService;
        }

        [BindProperty]
        public TaskAssignee TaskAssignee { get; set; }

        public SelectList TaskOptions { get; set; }
        public SelectList UserOptions { get; set; }

        private async Task LoadOptionsAsync()
        {
            var tasks = await _taskService.GetAllAsync();
            TaskOptions = new SelectList(tasks, "TaskId", "Title");

            var users = await _userService.GetAllAsync();
            UserOptions = new SelectList(users, "UserId", "Username");
        }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            TaskAssignee = await _taskAssigneeService.GetByIdAsync(id);
            if (TaskAssignee == null)
            {
                return NotFound();
            }
            await LoadOptionsAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadOptionsAsync();

                TempData["ErrorMessage"] = "Please check the entered information.";
                return Page();
            }

            try
            {
                await _taskAssigneeService.UpdateAsync(TaskAssignee);
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", $"An error occurred while updating the task assignee: {ex.Message}");
                await LoadOptionsAsync();

                TempData["ErrorMessage"] = "An error occurred while updating the task assignee. Please try again.";
                return Page();
            }
            
            TempData["SuccessMessage"] = "Task assignee updated successfully!";
            return RedirectToPage("Index");
        }       
    }
}