using CLanWork.Data.Entities;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Data.Interfaces
{
    public interface IClubRepository
    {
        Task<Club> CreateAsync(Club club);
        Task<Club?> GetByIdAsync(int id);
        Task<List<Club>> GetAllAsync();
        Task<List<Club>> GetByUserIdAsync(int userId);
        Task UpdateAsync(Club club);
        Task DeleteAsync(int id);
    }
}