using CLanWork.AI.Interfaces;
using CLanWork.Data;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Web.Pages.Users;

public class IndexModel : PageModel
{
    private readonly IUserService _userService;

    public IndexModel(IUserService userService)
    {
        _userService = userService;
    }

    public List<User> Users { get; set; } = new List<User>();
    public async Task OnGetAsync()
    {
        Users = await _userService.GetAllAsync();
    }
}

