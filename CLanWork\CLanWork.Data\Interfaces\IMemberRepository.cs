using CLanWork.Data.Entities;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Data.Interfaces
{
    public interface IMemberRepository
    {
        Task<Member> CreateAsync(Member member);
        Task<Member?> GetByIdAsync(int id);
        Task<List<Member>> GetAllAsync();
        Task<List<Member>> GetByClubIdAsync(int clubId);
        Task<List<Member>> GetByUserIdAsync(int userId);
        Task UpdateAsync(Member member);
        Task DeleteAsync(int id);
    }
}