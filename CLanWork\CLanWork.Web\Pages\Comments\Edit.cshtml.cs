using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace CLanWork.Web.Pages.Comments;

public class EditModel : PageModel
{
    private readonly ICommentService _commentService;

    public EditModel(ICommentService commentService)
    {
        _commentService = commentService;
    }

    [BindProperty]
    public Comment Comment { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        Comment = await _commentService.GetByIdAsync(id);

        if (Comment == null)
        {
            return NotFound();
        }

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        if (!ModelState.IsValid)
            return Page();

        Comment.CreatedAt = DateTime.Now;

        try
        {
            await _commentService.UpdateAsync(Comment);
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Có lỗi khi cập nhật bình luận: {ex.Message}");
            return Page();
        }

        TempData["SuccessMessage"] = "Bình luận đã được cập nhật thành công.";
        return RedirectToPage("Index");
    }
}