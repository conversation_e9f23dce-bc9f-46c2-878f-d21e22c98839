using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Web.Pages.Members
{
    public class CreateModel : PageModel
    {
        private readonly IMemberService _memberService;
        private readonly IClubService _clubService;
        private readonly IUserService _userService;

        public CreateModel(IMemberService memberService, IClubService clubService, IUserService userService)
        {
            _memberService = memberService;
            _clubService = clubService;
            _userService = userService;
        }

        [BindProperty]
        public Member Member { get; set; }

        public SelectList ClubOptions { get; set; }
        public SelectList UserOptions { get; set; }
        public SelectList RoleOptions { get; set; }

        private async Task LoadOptionsAsync()
        {
            var clubs = await _clubService.GetAllAsync();
            ClubOptions = new SelectList(clubs, "ClubId", "Name");

            var users = await _userService.GetAllAsync();
            UserOptions = new SelectList(users, "UserId", "Username");

            var roles = new List<string> { "Member", "Admin" };
            RoleOptions = new SelectList(roles);
        }

        public async Task<IActionResult> OnGetAsync()
        {
            await LoadOptionsAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadOptionsAsync();

                TempData["ErrorMessage"] = "Please correct the errors in the form.";
                return Page();
            }

            await _memberService.CreateAsync(Member);

            TempData["SuccessMessage"] = "Member created successfully.";
            return RedirectToPage("./Index");
        }
    }
}