﻿@page
@model CLanWork.Web.Pages.Users.CreateModel
@{
    ViewData["Title"] = "Tạo người dùng";
}

<h2>Tạo người dùng mới</h2>

<form method="post" id="createForm">
    <div class="form-group">
        <label asp-for="User.Username"></label>
        <input asp-for="User.Username" class="form-control" />
        <span asp-validation-for="User.Username" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="User.Email"></label>
        <input asp-for="User.Email" class="form-control" />
        <span asp-validation-for="User.Email" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="User.Password"></label>
        <input asp-for="User.Password" class="form-control" type="password" />
        <span asp-validation-for="User.Password" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="User.Role"></label>
        <select asp-for="User.Role" class="form-control" asp-items="Model.RolesOptions"></select>
        <span asp-validation-for="User.Role" class="text-danger"></span>
    </div>

    <button type="submit" class="btn btn-success">Lưu</button>
    <a asp-page="Index" class="btn btn-secondary">Hủy</a>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}