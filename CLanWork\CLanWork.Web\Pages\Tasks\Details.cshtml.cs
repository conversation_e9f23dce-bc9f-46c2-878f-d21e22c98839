using CLanWork.AI.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace CLanWork.Web.Pages.Tasks;

public class DetailsModel : PageModel
{
    private readonly ITaskService _taskService;

    public DetailsModel(ITaskService taskService)
    {
        _taskService = taskService;
    }

    [BindProperty(SupportsGet = true)]
    public int Id { get; set; }

    public Data.Entities.Task Task { get; set; }

    public async Task<IActionResult> OnGetAsync()
    {
        Task = await _taskService.GetByIdAsync(Id);
    
        if (Task == null)
        {
            return NotFound();
        }

        return Page();
    }
}
