using CLanWork.Data.Entities;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.AI.Interfaces
{
    public interface IProofService
    {
        Task<Proof> CreateAsync(Proof proof);
        Task<Proof?> GetByIdAsync(int id);
        Task<List<Proof>> GetAllAsync();
        Task<List<Proof>> GetByTaskIdAsync(int taskId);
        Task UpdateAsync(Proof proof);
        Task DeleteAsync(int id);
        Task ApproveAsync(int proofId);
    }
}