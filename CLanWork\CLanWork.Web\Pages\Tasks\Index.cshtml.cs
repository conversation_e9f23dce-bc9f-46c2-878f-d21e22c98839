using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using CLanWork.Data;
using EntityTask = CLanWork.Data.Entities.Task;
using CLanWork.AI.Interfaces;

namespace CLanWork.Web.Pages.Tasks;

public class IndexModel : PageModel
{
    private readonly ITaskService _taskService;

    public IndexModel(ITaskService taskService)
    {
        _taskService = taskService;
    }

    public IList<EntityTask> Tasks { get; private set; } = new List<EntityTask>();

    public async Task OnGetAsync()
    {
        Tasks = await _taskService.GetAllAsync();
    }
}