using Microsoft.AspNetCore.Mvc.RazorPages;
using CLanWork.Data.Entities;
using CLanWork.AI.Interfaces;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Web.Pages.Clubs;

public class IndexModel : PageModel
{
    private readonly IClubService _clubService;

    public IndexModel(IClubService clubService)
    {
        _clubService = clubService;
    }

    public List<Club> Clubs { get; set; } = new();

    public async Task OnGetAsync()
    {
        Clubs = await _clubService.GetAllAsync();
    }
}