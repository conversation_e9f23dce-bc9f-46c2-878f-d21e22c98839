using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CLanWork.Data.Entities;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.AI.Interfaces
{
    public interface IUserService
    {
        Task<User> CreateAsync(User user);
        Task<User?> GetByIdAsync(int id);
        Task<User?> GetByEmailAsync(string email);
        Task<List<User>> GetAllAsync();
        Task UpdateAsync(User user);
        Task DeleteAsync(int id);
    }
}