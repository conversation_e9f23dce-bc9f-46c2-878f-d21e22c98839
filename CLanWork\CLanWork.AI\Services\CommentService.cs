using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using CLanWork.Data.Interfaces;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.AI.Services
{
    public class CommentService : ICommentService
    {
        private readonly ICommentRepository _repository;

        public CommentService(ICommentRepository repository)
        {
            _repository = repository;
        }
        public async Task<Comment> CreateAsync(Comment comment)
        {
            return await _repository.CreateAsync(comment);
        }

        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id);
        }

        public async Task<List<Comment>> GetAllAsync()
        {
            return await _repository.GetAllAsync();
        }

        public async Task<Comment?> GetByIdAsync(int id)
        {
            return await _repository.GetByIdAsync(id);
        }

        public async Task<List<Comment>> GetByTaskIdAsync(int taskId)
        {
            return await _repository.GetByTaskIdAsync(taskId);
        }

        public async Task UpdateAsync(Comment comment)
        {
            await _repository.UpdateAsync(comment);
        }
    }
}