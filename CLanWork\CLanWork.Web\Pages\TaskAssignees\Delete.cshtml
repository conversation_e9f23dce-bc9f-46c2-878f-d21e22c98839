@page
@model CLanWork.Web.Pages.TaskAssignees.DeleteModel
@{
    ViewData["Title"] = "<PERSON>óa Người Giao V<PERSON>";
}

<h2><PERSON><PERSON><PERSON>ờ<PERSON> Gia<PERSON></h2>

<div>
    <h4><PERSON><PERSON><PERSON> có chắc muốn xóa người giao việc sau?</h4>

    <dl class="row">
        <dt class="col-sm-2">Task</dt>
        <dd class="col-sm-10">@Model.TaskAssignee.Task?.Title</dd>

        <dt class="col-sm-2">User</dt>
        <dd class="col-sm-10">@Model.TaskAssignee.User?.Username</dd>
    </dl>

    <form method="post">
        <input type="hidden" asp-for="TaskAssignee.Id" />
        <button type="submit" class="btn btn-danger">Xóa</button>
        <a asp-page="Index" class="btn btn-secondary">Hủy</a>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}