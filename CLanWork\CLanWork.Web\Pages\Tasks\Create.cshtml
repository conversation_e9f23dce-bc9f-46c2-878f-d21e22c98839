﻿@page
@model CLanWork.Web.Pages.Tasks.CreateModel
@{
    ViewData["Title"] = "Tạo Task Mới";
}

<h2>Tạo Task Mới</h2>

<form method="post">
    <div class="form-group">
        <label asp-for="Task.Title"></label>
        <input asp-for="Task.Title" class="form-control" />
        <span asp-validation-for="Task.Title" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="Task.Description"></label>
        <textarea asp-for="Task.Description" class="form-control"></textarea>
    </div>

    <div class="form-group">
        <label asp-for="Task.DueDate"></label>
        <input asp-for="Task.DueDate" class="form-control" type="date" />
        <span asp-validation-for="Task.DueDate" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="Task.Priority"></label>
        <select asp-for="Task.Priority" class="form-control" asp-items="Model.PriorityOptions"></select>
        <span asp-validation-for="Task.Priority" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="Task.Status"></label>
        <select asp-for="Task.Status" class="form-control" asp-items="Model.StatusOptions"></select>
        <span asp-validation-for="Task.Status" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="Task.ClubId">Club</label>
        <select asp-for="Task.ClubId" asp-items="Model.ClubOptions" class="form-control"></select>
    </div>

    <button type="submit" class="btn btn-primary">Tạo Task</button>
    <a asp-page="Index" class="btn btn-secondary">Hủy</a>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}