using CLanWork.Data.Entities;
using CLanWork.Data.Interfaces;
using Microsoft.EntityFrameworkCore;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Data.Repositories
{
    public class ClubRepository : IClubRepository
    {
        private readonly CLanWorkContext _context;

        public ClubRepository(CLanWorkContext context)
        {
            _context = context;
        }

        public async Task<Club> CreateAsync(Club club)
        {
            int maxId = await _context.Clubs.AnyAsync()
                ? await _context.Clubs.MaxAsync(c => c.ClubId)
                : 0;

            club.ClubId = maxId + 1;
            _context.Clubs.Add(club);
            await _context.SaveChangesAsync();
            return club;
        }

        public async Task DeleteAsync(int id)
        {
            var club = await _context.Clubs.FindAsync();

            if (club != null)
            {
                _context.Clubs.Remove(club);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<List<Club>> GetAllAsync()
        {
            return await _context.Clubs.ToListAsync();
        }

        public async Task<Club?> GetByIdAsync(int id)
        {
            return await _context.Clubs.Include(c => c.Members)
                                        .Include(c => c.Tasks)
                                        .FirstOrDefaultAsync(c => c.ClubId == id);
        }

        public async Task<List<Club>> GetByUserIdAsync(int userId)
        {
            return await _context.Members
                .Where(m => m.UserId == userId)
                .Select(m => m.Club)
                .Distinct()
                .ToListAsync();
        }

        public async Task UpdateAsync(Club club)
        {
            _context.Clubs.Update(club);
            await _context.SaveChangesAsync();
        }
    }
}