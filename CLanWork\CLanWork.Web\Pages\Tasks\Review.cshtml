﻿@page "{id:int}"
@model CLanWork.Web.Pages.Tasks.ReviewModel
@{
    ViewData["Title"] = "Review Task Proof";
}

<h2>Review Proof for Task: @Model.Task.Title</h2>
<p><strong>Status:</strong> @Model.Task.Status</p>
<p><strong>Deadline:</strong> @(Model.Task.DueDate?.ToString("dd/MM/yyyy") ?? "Chưa có")</p>

<form method="post" enctype="multipart/form-data">
    <div class="form-group">
        <label>Upload Proof (image/link/file):</label>
        <input type="file" name="ProofFile" class="form-control" />
    </div>

    <button type="submit" class="btn btn-primary">Nộp Proof cho AI review</button>
    <a asp-page="Index" class="btn btn-secondary">Huỷ</a>
</form>

@if (Model.AIResult != null)
{
    <hr />
    <h4>AI Review Result:</h4>
    <p><strong>Status:</strong> @Model.AIResult.Result</p>
    <p><strong>AI Comment:</strong> @Model.AIResult.AIComment</p>
}