using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Web.Pages.TaskAssignees
{
    public class IndexModel : PageModel
    {
        private readonly ITaskAssigneeService _taskAssigneeService;

        public IndexModel(ITaskAssigneeService taskAssigneeService)
        {
            _taskAssigneeService = taskAssigneeService;
        }

        public IList<TaskAssignee> TaskAssignees { get; set; } = new List<TaskAssignee>();

        public async Task OnGetAsync()
        {
            TaskAssignees = await _taskAssigneeService.GetAllAsync();
        }
    }
}