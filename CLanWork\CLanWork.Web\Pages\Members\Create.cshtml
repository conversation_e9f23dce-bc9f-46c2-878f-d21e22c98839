@page
@model CLanWork.Web.Pages.Members.CreateModel
@{
    ViewData["Title"] = "Tạo Member Mới";
}

<h2 class="mb-4">Tạo Member Mới</h2>

<form method="post">
    <div class="form-group mb-3">
        <label asp-for="Member.UserId" class="form-label">User</label>
        <select asp-for="Member.UserId" class="form-control" asp-items="Model.UserOptions"></select>
        <span asp-validation-for="Member.UserId" class="text-danger"></span>
    </div>

    <div class="form-group mb-3">
        <label asp-for="Member.ClubId" class="form-label">Club</label>
        <select asp-for="Member.ClubId" class="form-control" asp-items="Model.ClubOptions"></select>
        <span asp-validation-for="Member.ClubId" class="text-danger"></span>
    </div>

    <div class="form-group mb-3">
        <label asp-for="Member.Role" class="form-label">Role</label>
        <select asp-for="Member.Role" class="form-control" asp-items="Model.RoleOptions"></select>
        <span asp-validation-for="Member.Role" class="text-danger"></span>
    </div>

    <button type="submit" class="btn btn-primary">Tạo Member</button>
    <a asp-page="Index" class="btn btn-secondary ms-2">Hủy</a>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
