using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using CLanWork.Data;
using CLanWork.Data.Entities;
using CLanWork.AI.Interfaces;

namespace CLanWork.Web.Pages.Clubs;

public class DeleteModel : PageModel
{
    private readonly IClubService _clubService;

    public DeleteModel(IClubService clubService)
    {
        _clubService = clubService;
    }

    [BindProperty]
    public Club Club { get; set; } = new();

    

    public async Task<IActionResult> OnGetAsync(int id)
    {
        Club = await _clubService.GetByIdAsync(id);
        return Club == null ? NotFound() : Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        if (Club == null || Club.ClubId <= 0)
        {
            return NotFound();
        }
        
        await _clubService.DeleteAsync(Club.ClubId);

        TempData["SuccessMessage"] = "Club deleted successfully.";
        return RedirectToPage("Index");
    }
}