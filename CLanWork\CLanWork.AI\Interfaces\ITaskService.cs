using EntityTask = CLanWork.Data.Entities.Task;

namespace CLanWork.AI.Interfaces
{
    public interface ITaskService
    {
        Task<EntityTask> CreateAsync(EntityTask task);
        Task<EntityTask?> GetByIdAsync(int id);
        Task<List<EntityTask>> GetAllAsync();
        Task<List<EntityTask>> GetByClubIdAsync(int clubId);
        Task UpdateAsync(EntityTask task);
        Task DeleteAsync(int id);
        Task ChangeStatusAsync(int taskId, string newStatus);
        Task<List<EntityTask>> GetUpcomingTasksAsync(DateTime from, DateTime to);
    }
}