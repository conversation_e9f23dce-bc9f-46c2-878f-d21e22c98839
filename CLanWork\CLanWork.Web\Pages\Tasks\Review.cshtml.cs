using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using CLanWork.Data;
using CLanWork.Data.Entities;
using System.IO;
using CLanWork.Web.Services;
using CLanWork.AI.Interfaces;

namespace CLanWork.Web.Pages.Tasks;

public class ReviewModel : PageModel
{
    private readonly ITaskService _taskService;
    private readonly IAIReviewResultService _aiReviewResultService;
    private readonly AIReviewService _aiReviewService;

    public ReviewModel(ITaskService taskService, IAIReviewResultService aIReviewResultService, AIReviewService aiReviewService)
    {
        _taskService = taskService;
        _aiReviewResultService = aIReviewResultService;
        _aiReviewService = aiReviewService;
    }

    [BindProperty(SupportsGet = true)]
    public int Id { get; set; }

    public Data.Entities.Task Task { get; set; }

    [BindProperty]
    public IFormFile ProofFile { get; set; }

    public AIReviewResult? AIResult { get; set; }

    public async Task<IActionResult> OnGetAsync()
    {
        Task = await _taskService.GetByIdAsync(Id);

        if (Task == null) return NotFound();

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        Task = await _taskService.GetByIdAsync(Id);

        if (Task == null) return NotFound();

        if (ProofFile == null || ProofFile.Length == 0)
        {
            ModelState.AddModelError("ProofFile", "Bạn phải chọn một file để upload.");
            return Page();
        }

        string fileName = $"{Guid.NewGuid()}_{ProofFile.FileName}";
        string relativePath = Path.Combine("uploads", fileName);
        string absolutePath = Path.Combine("wwwroot", relativePath);

        using (var stream = new FileStream(absolutePath, FileMode.Create))
        {
            await ProofFile.CopyToAsync(stream);
        }

        AIResult = await _aiReviewService.ReviewAsync(Task.TaskId, fileName);
        await _aiReviewResultService.CreateAsync(AIResult);

        TempData["SuccessMessage"] = "Đã gửi yêu cầu đánh giá AI thành công.";
        return Page();      
    }
}
