using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Web.Pages.TaskAssignees
{
    public class CreateModel : PageModel
    {
        private readonly ITaskAssigneeService _taskAssigneeService;
        private readonly IUserService _userService;
        private readonly ITaskService _taskService;

        public CreateModel(
            ITaskAssigneeService taskAssigneeService,
            IUserService userService,
            ITaskService taskService)
        {
            _taskAssigneeService = taskAssigneeService;
            _userService = userService;
            _taskService = taskService;
        }

        [BindProperty]
        public TaskAssignee TaskAssignee { get; set; }

        public SelectList TaskOptions { get; set; }
        public SelectList UserOptions { get; set; }

        private async Task LoadOptionsAsync()
        {
            var tasks = await _taskService.GetAllAsync();
            TaskOptions = new SelectList(tasks, "TaskId", "Title");

            var users = await _userService.GetAllAsync();
            UserOptions = new SelectList(users, "UserId", "Username");
        }

        public async Task<IActionResult> OnGetAsync()
        {
            await LoadOptionsAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadOptionsAsync();

                TempData["ErrorMessage"] = "Please check the entered information.";
                return Page();
            }

            await _taskAssigneeService.CreateAsync(TaskAssignee);
            
            TempData["SuccessMessage"] = "Task assignee created successfully!";
            return RedirectToPage("Index");
        }
    }
}
