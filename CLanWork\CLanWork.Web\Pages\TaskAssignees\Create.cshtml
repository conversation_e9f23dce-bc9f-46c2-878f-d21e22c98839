@page
@model CLanWork.Web.Pages.TaskAssignees.CreateModel
@{
    ViewData["Title"] = "Tạo Task Assignee Mới";
}

<h2 class="mb-4">Tạo Task Assignee Mới</h2>

<form method="post">
    <div class="form-group mb-3">
        <label asp-for="TaskAssignee.TaskId" class="form-label">Task</label>
        <select asp-for="TaskAssignee.TaskId" class="form-control" asp-items="Model.TaskOptions"></select>
        <span asp-validation-for="TaskAssignee.TaskId" class="text-danger"></span>
    </div>

    <div class="form-group mb-3">
        <label asp-for="TaskAssignee.UserId" class="form-label">User</label>
        <select asp-for="TaskAssignee.UserId" class="form-control" asp-items="Model.UserOptions"></select>
        <span asp-validation-for="TaskAssignee.UserId" class="text-danger"></span>
    </div>

    <button type="submit" class="btn btn-primary">Tạo Task Assignee</button>
    <a asp-page="Index" class="btn btn-secondary ms-2">Hủy</a>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
