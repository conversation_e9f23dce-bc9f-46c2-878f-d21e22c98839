﻿@page "{id:int}"
@model CLanWork.Web.Pages.Tasks.DetailsModel
@{
    ViewData["Title"] = "Chi tiết nhiệm vụ";
}

<h2>@Model.Task.Title</h2>

<div class="card p-3 mb-4 shadow-sm">
    <p><strong><PERSON><PERSON> tả:</strong> @Model.Task.Description</p>
    <p><strong>Trạng thái:</strong> @Model.Task.Status</p>
    <p><strong><PERSON><PERSON> ưu tiên:</strong> @Model.Task.Priority</p>
    <p><strong>Hạn chót:</strong> @(Model.Task.DueDate?.ToString("dd/MM/yyyy")?? "Chưa có")</p>
</div>

@if (Model.Task.Comments.Any())
{
    <div class="card p-3 shadow-sm">
        <h5>Bình luận</h5>
        
        <ul class="list-unstyled">
            @foreach (var comment in Model.Task.Comments.OrderByDescending(c => c.CreatedAt))
            {
                <li class="mb-2 border-bottom pb-2">
                    <strong>@comment.User.Username:</strong> @comment.Content
                    <div class="text-muted" style="font-size: 0.8rem">@comment.CreatedAt.ToString("dd/MM/yyyy HH:mm")</div>
                </li>
            }
        </ul>
    </div>
}
else
{
    <p class="text-muted">Chưa có bình luận nào.</p>
}

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Thêm sự kiện cho nút bình luận nếu cần
            const commentForm = document.getElementById("commentForm");
            if (commentForm) {
                commentForm.addEventListener("submit", function (event) {
                    event.preventDefault();
                    // Xử lý gửi bình luận ở đây
                });
            }
        });
    </script>
}