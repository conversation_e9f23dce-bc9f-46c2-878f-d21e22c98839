using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace CLanWork.Web.Pages.TaskAssignees
{
    public class DeleteModel : PageModel
    {
        private readonly ITaskAssigneeService _taskAssigneeService;

        public DeleteModel(ITaskAssigneeService taskAssigneeService)
        {
            _taskAssigneeService = taskAssigneeService;
        }

        [BindProperty]
        public TaskAssignee TaskAssignee { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            TaskAssignee = await _taskAssigneeService.GetByIdAsync(id);

            if (TaskAssignee == null)
            {
                return NotFound();
            }
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (TaskAssignee == null)
            {
                return NotFound();
            }

            await _taskAssigneeService.DeleteAsync(TaskAssignee.Id);

            TempData["SuccessMessage"] = "Task assignee deleted successfully!";
            return RedirectToPage("Index");
        }
    }
}