using CLanWork.AI.Interfaces;
using CLanWork.Data;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace CLanWork.Web.Pages.Users
{
    public class DeleteModel : PageModel
    {
        private readonly IUserService _userService;

        public DeleteModel(IUserService userService)
        {
            _userService = userService;
        }

        [BindProperty]
        public User User { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            User = await _userService.GetByIdAsync(id);

            if (User == null)
            {
                return NotFound();
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var existingUser = await _userService.GetByIdAsync(User.UserId);

            if (existingUser == null)
            {
                return NotFound();
            }

            await _userService.DeleteAsync(User.UserId);

            TempData["SuccessMessage"] = "User deleted successfully.";
            return RedirectToPage("Index");
        }
    }
}
