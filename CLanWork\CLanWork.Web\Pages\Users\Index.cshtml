﻿@page
@model CLanWork.Web.Pages.Users.IndexModel
@{
    ViewData["Title"] = "Danh sách người dùng";
}

<h2>Ngư<PERSON><PERSON> dùng</h2>
<a asp-page="Create" class="btn btn-primary mb-3">Tạo mới</a>

<table class="table table-bordered">
    <thead>
        <tr>
            <th>Username</th>
            <th>Email</th>
            <th>Role</th>
            <th></th>
        </tr>
    </thead>

    <tbody>
        @foreach (var user in Model.Users)
        {
            <tr>
                <td>@user.Username</td>
                <td>@user.Email</td>
                <td>@user.Role</td>
                <td>
                    <a asp-page="Edit" asp-route-id="@user.UserId">Sửa</a> |
                    <a asp-page="Details" asp-route-id="@user.UserId">Chi tiết</a> |
                    <a asp-page="Delete" asp-route-id="@user.UserId">Xóa</a>
                </td>
            </tr>
        }
    </tbody>
</table>

@if (Model.Users.Count == 0)
{
    <tr>
        <td colspan="4" class="text-center">Không có người dùng nào.</td>
    </tr>
}

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}