using CLanWork.Data.Entities;
using UglyToad.PdfPig;
using System.Text;

namespace CLanWork.Web.Services;

public class AIReviewService
{
    public async Task<AIReviewResult> ReviewAsync(int taskId, string filePath)
    {
        await System.Threading.Tasks.Task.Delay(500); // mô phỏng thời gian phản hồi

        string extension = Path.GetExtension(filePath).ToLower();
        string content = "";

        try
        {
            if (extension == ".txt")
            {
                content = await File.ReadAllTextAsync(filePath);
            }
            else if (extension == ".pdf")
            {
                var sb = new StringBuilder();
                using var doc = PdfDocument.Open(filePath);
                foreach (var page in doc.GetPages())
                {
                    sb.Append(page.Text);
                }
                content = sb.ToString();
            }
        }
        catch (Exception ex)
        {
            return new AIReviewResult
            {
                TaskId = taskId,
                Result = "REJECTED",
                AIComment = $"Error reading file: {ex.Message}",
                ReviewedAt = DateTime.Now
            };
        }

        // 🧠 Mô phỏng phân tích nội dung
        string result;
        string comment;

        if (string.IsNullOrWhiteSpace(content))
        {
            result = "REJECTED";
            comment = "File is empty or unreadable.";
        }
        else if (content.Length < 100)
        {
            result = "NEEDS_EDIT";
            comment = "Proof is too short. Please elaborate more.";
        }
        else
        {
            result = "ACCEPTED";
            comment = "Proof seems valid and well-detailed.";
        }

        return new AIReviewResult
        {
            TaskId = taskId,
            Result = result,
            AIComment = comment,
            ReviewedAt = DateTime.Now
        };
    }
}
