﻿@page "{id:int}"
@model CLanWork.Web.Pages.Tasks.DeleteModel
@{
    ViewData["Title"] = "Xoá Task";
}

<h2><PERSON><PERSON><PERSON> có chắc muốn xoá task này?</h2>

<div>
    <h4>@Model.Task.Title</h4>
    <p>Deadline: @(Model.Task.DueDate?.ToString("dd/MM/yyyy") ?? "Chưa có")</p>
    <p>Status: @Model.Task.Status</p>
</div>

<form method="post">
    <input type="hidden" asp-for="Task.TaskId" />
    <button type="submit" class="btn btn-danger">Xoá</button>
    <a asp-page="Index" class="btn btn-secondary">Huỷ</a>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}