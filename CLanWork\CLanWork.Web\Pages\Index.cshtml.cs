using CLanWork.AI.Interfaces;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Task = System.Threading.Tasks.Task;
using EntityTask = CLanWork.Data.Entities.Task;
using CLanWork.Data.Entities;

namespace CLanWork.Web.Pages;

public class IndexModel : PageModel
{
    private readonly ITaskService _taskService;
    private readonly IProofService _proofService;

    public IndexModel(ITaskService taskService, IProofService proofService)
    {
        _taskService = taskService;
        _proofService = proofService;
    }

    public List<EntityTask> UpcomingTasks { get; set; }
    public List<Proof> Proofs { get; set; }

    public async Task OnGetAsync()
    {
        var now = DateTime.Now;
        var soon = now.AddDays(7); // nhiệm vụ hết hạn trong vòng 7 ngày tới

        UpcomingTasks = await _taskService.GetUpcomingTasksAsync(now, soon);
        Proofs = await _proofService.GetAllAsync();
    }
}
