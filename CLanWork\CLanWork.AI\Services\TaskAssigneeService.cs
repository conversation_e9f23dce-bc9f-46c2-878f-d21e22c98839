using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using CLanWork.Data.Interfaces;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.AI.Services
{
    public class TaskAssigneeService : ITaskAssigneeService
    {
        private readonly ITaskAssigneeRepository _repository;
        public TaskAssigneeService(ITaskAssigneeRepository repository)
        {
            _repository = repository;
        }

        public async Task<TaskAssignee> CreateAsync(TaskAssignee assignee)
        {
            return await _repository.CreateAsync(assignee);
        }

        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id);
        }

        public async Task<List<TaskAssignee>> GetAllAsync()
        {
            return await _repository.GetAllAsync();
        }

        public async Task<List<User>> GetAssigneesByTaskIdAsync(int taskId)
        {
            return await _repository.GetAssigneesByTaskIdAsync(taskId);
        }

        public async Task<TaskAssignee?> GetByIdAsync(int id)
        {
            return await _repository.GetByIdAsync(id);
        }

        public async Task<List<Data.Entities.Task>> GetTasksByUserIdAsync(int userId)
        {
            return await _repository.GetTasksByUserIdAsync(userId);
        }

        public async Task UpdateAsync(TaskAssignee assignee)
        {
            await _repository.UpdateAsync(assignee);
        }
    }
}