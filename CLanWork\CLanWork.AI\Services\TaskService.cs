using CLanWork.AI.Interfaces;
using CLanWork.Data.Interfaces;

namespace CLanWork.AI.Services
{
    public class TaskService : ITaskService
    {
        private readonly ITaskRepository _repository;

        public TaskService(ITaskRepository repository)
        {
            _repository = repository;
        }

        public async Task ChangeStatusAsync(int taskId, string newStatus)
        {
            var task = await _repository.GetByIdAsync(taskId);

            if (task == null)
            {
                throw new Exception($"Task with ID {taskId} not found.");
            }

            task.Status = newStatus;
            await _repository.UpdateAsync(task);
        }

        public async Task<Data.Entities.Task> CreateAsync(Data.Entities.Task task)
        {
            return await _repository.CreateAsync(task);
        }

        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id);
        }

        public async Task<List<Data.Entities.Task>> GetAllAsync()
        {
            return await _repository.GetAllAsync();
        }

        public async Task<List<Data.Entities.Task>> GetByClubIdAsync(int clubId)
        {
            return await _repository.GetByClubIdAsync(clubId);
        }

        public async Task<Data.Entities.Task?> GetByIdAsync(int id)
        {
            return await _repository.GetByIdAsync(id);
        }

        public async Task<List<Data.Entities.Task>> GetUpcomingTasksAsync(DateTime from, DateTime to)
        {
            var tasks = await _repository.GetUpcomingTasksAsync(from, to);

            return tasks
                .Where(t => t.DueDate.HasValue && t.DueDate >= from && t.DueDate <= to)
                .OrderBy(t => t.DueDate)
                .ToList();
        }

        public async Task UpdateAsync(Data.Entities.Task task)
        {
            await _repository.UpdateAsync(task);
        }
    }
}