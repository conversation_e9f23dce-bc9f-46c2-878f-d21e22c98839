using CLanWork.Data.Entities;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Data.Interfaces
{
    public interface ICommentRepository
    {
        Task<Comment> CreateAsync(Comment comment);
        Task<Comment?> GetByIdAsync(int id);
        Task<List<Comment>> GetAllAsync();
        Task<List<Comment>> GetByTaskIdAsync(int taskId);
        Task UpdateAsync(Comment comment);
        Task DeleteAsync(int id);
    }
}