using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using CLanWork.Data.Interfaces;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.AI.Services
{
    public class UserService : IUserService
    {
        private readonly IUserRepository _repository;

        public UserService(IUserRepository repository)
        {
            _repository = repository;
        }

        public async Task<User> CreateAsync(User user)
        {
            return await _repository.CreateAsync(user);
        }

        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id);
        }

        public async Task<List<User>> GetAllAsync()
        {
            return await _repository.GetAllAsync();
        }

        public async Task<User?> GetByEmailAsync(string email)
        {
            return await _repository.GetByEmailAsync(email);
        }

        public async Task<User?> GetByIdAsync(int id)
        {
            return await _repository.GetByIdAsync(id);
        }

        public async Task UpdateAsync(User user)
        {
            await _repository.UpdateAsync(user);
        }
    }
}