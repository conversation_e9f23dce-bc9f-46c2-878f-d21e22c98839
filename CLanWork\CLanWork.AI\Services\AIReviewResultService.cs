using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using CLanWork.Data.Interfaces;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.AI.Services
{
    public class AIReviewResultService : IAIReviewResultService
    {
        private readonly IAIReviewResultRepository _repository;

        public AIReviewResultService(IAIReviewResultRepository repository)
        {
            _repository = repository;
        }

        public async Task<AIReviewResult> CreateAsync(AIReviewResult result)
        {
            return await _repository.CreateAsync(result);
        }

        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id);
        }

        public async Task<List<AIReviewResult>> GetAllAsync()
        {
            return await _repository.GetAllAsync();
        }

        public async Task<AIReviewResult?> GetByIdAsync(int id)
        {
            return await _repository.GetByIdAsync(id);
        }

        public async Task<List<AIReviewResult>> GetByTaskIdAsync(int taskId)
        {
            return await _repository.GetByTaskIdAsync(taskId);
        }

        public async Task UpdateAsync(AIReviewResult result)
        {
            await _repository.UpdateAsync(result);
        }
    }
}