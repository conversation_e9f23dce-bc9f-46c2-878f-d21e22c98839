using CLanWork.AI.Interfaces;
using CLanWork.Data;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;

namespace CLanWork.Web.Pages.Users
{
    public class EditModel : PageModel
    {
        private readonly IUserService _userService;

        public SelectList RolesOptions { get; set; }

        public EditModel(IUserService userService)
        {
            _userService = userService;
        }

        [BindProperty]
        public User User { get; set; }

        private void LoadRolesOptions()
        {
            RolesOptions = new SelectList(new[] { "Admin", "Club Admin", "Club Member" });
        }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            User = await _userService.GetByIdAsync(id);

            if (User == null)
            {
                return NotFound();
            }

            LoadRolesOptions();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                LoadRolesOptions();
                return Page();
            }

            var existingUser = await _userService.GetByIdAsync(User.UserId);

            if (existingUser == null)
            {
                return NotFound();
            }

            existingUser.Username = User.Username;
            existingUser.Email = User.Email;
            existingUser.Password = User.Password;
            existingUser.Role = User.Role;

            await _userService.UpdateAsync(existingUser);

            TempData["SuccessMessage"] = "User updated successfully.";
            return RedirectToPage("Index");
        }
    }

}
