using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using EntityTask = CLanWork.Data.Entities.Task;
using CLanWork.AI.Interfaces;

namespace CLanWork.Web.Pages.Tasks;

public class EditModel : PageModel
{
    private readonly ITaskService _taskService;
    private readonly IClubService _clubService;

    public EditModel(ITaskService taskService, IClubService clubService)
    {
        _taskService = taskService;
        _clubService = clubService;
    }

    [BindProperty]
    public EntityTask Task { get; set; }

    public SelectList PriorityOptions { get; set; }
    public SelectList StatusOptions { get; set; }
    public SelectList ClubOptions { get; set; }



    public async Task<IActionResult> OnGetAsync(int id)
    {
        Task = await _taskService.GetByIdAsync(id);

        if (Task == null)
            return NotFound();

        await LoadOptionsAsync();
        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        if (!ModelState.IsValid)
        {
            await LoadOptionsAsync();
            return Page();
        }

        if (Task.DueDate.HasValue)
        {
            Task.DueDate = DateTime.SpecifyKind(Task.DueDate.Value, DateTimeKind.Utc);
        }

        try
        {
            await _taskService.UpdateAsync(Task);
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Có lỗi khi cập nhật: {ex.Message}");
            await LoadOptionsAsync();
            return Page();
        }

        TempData["SuccessMessage"] = "Task updated successfully.";
        return RedirectToPage("Index");
    }

    private async Task LoadOptionsAsync()
    {
        var clubs = await _clubService.GetAllAsync();

        PriorityOptions = new SelectList(new[] { "Low", "Medium", "High" });
        StatusOptions = new SelectList(new[] { "Todo", "InProgress", "Done" });
        ClubOptions = new SelectList(clubs, "ClubId", "Name");
    }
}