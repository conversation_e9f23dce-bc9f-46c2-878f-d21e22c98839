@page
@model CLanWork.Web.Pages.TaskAssignees.EditModel
@{
    ViewData["Title"] = "Chỉnh Sửa Giao <PERSON>";
}

<h2>Chỉnh Sửa G<PERSON>o <PERSON></h2>

<form method="post">
    <input type="hidden" asp-for="TaskAssignee.Id" />
    <input type="hidden" asp-for="TaskAssignee.TaskId" />
    <input type="hidden" asp-for="TaskAssignee.UserId" />

    <div class="form-group mb-3">
        <label asp-for="TaskAssignee.TaskId" class="form-label"></label>
        <select asp-for="TaskAssignee.TaskId" class="form-select" asp-items="Model.TaskOptions"></select>
        <span asp-validation-for="TaskAssignee.TaskId" class="text-danger"></span>
    </div>

    <div class="form-group mb-3">
        <label asp-for="TaskAssignee.UserId" class="form-label"></label>
        <select asp-for="TaskAssignee.UserId" class="form-select" asp-items="Model.UserOptions"></select>
        <span asp-validation-for="TaskAssignee.UserId" class="text-danger"></span>
    </div>

    <button type="submit" class="btn btn-success">Cập nhật</button>
    <a asp-page="Index" class="btn btn-secondary ms-2">Hủy</a>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
