@page "{id:int}"
@model CLanWork.Web.Pages.Comments.DeleteModel
@{
    ViewData["Title"] = "Xóa Comment";
}

<h2>Xóa Comment</h2>

<div>
    <h4>Bạn có chắc muốn xóa comment sau?</h4>
    <p>@Model.Comment.Content</p>

    <form method="post">
        <input type="hidden" asp-for="Comment.CommentId" />
        <button type="submit" class="btn btn-danger">Xóa</button>
        <a asp-page="Index" class="btn btn-secondary">Hủy</a>
    </form>
</div>
