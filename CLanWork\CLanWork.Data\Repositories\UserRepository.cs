using CLanWork.Data.Entities;
using CLanWork.Data.Interfaces;
using Microsoft.EntityFrameworkCore;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Data.Repositories
{
    public class UserRepository : IUserRepository
    {
        private readonly CLanWorkContext _context;

        public UserRepository(CLanWorkContext context)
        {
            _context = context;
        }

        public async Task<User> CreateAsync(User user)
        {
            int maxId = await _context.Users.AnyAsync()
                ? await _context.Users.MaxAsync(u => u.UserId)
                : 0;

            user.UserId = maxId + 1;
            _context.Users.Add(user);
            await _context.SaveChangesAsync();
            return user;
        }

        public async Task DeleteAsync(int id)
        {
            var user = await _context.Users.FindAsync(id);

            if (user != null)
            {
                _context.Users.Remove(user);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<List<User>> GetAllAsync()
        {
            return await _context.Users.ToListAsync();
        }

        public async Task<User?> GetByEmailAsync(string email)
        {
            return await _context.Users
                .Include(u => u.Memberships)
                .FirstOrDefaultAsync(u => u.Email == email);
        }

        public async Task<User?> GetByIdAsync(int id)
        {
            return await _context.Users
                .Include(u => u.Memberships).ThenInclude(m => m.Club)
                .Include(u => u.Comments)
                .Include(u => u.AssignedTasks).ThenInclude(a => a.Task)
                .FirstOrDefaultAsync(u => u.UserId == id);
        }

        public async Task UpdateAsync(User user)
        {
            _context.Users.Update(user);
            await _context.SaveChangesAsync();
        }
    }
}