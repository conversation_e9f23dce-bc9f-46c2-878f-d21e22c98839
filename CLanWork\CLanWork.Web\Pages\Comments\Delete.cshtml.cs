using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace CLanWork.Web.Pages.Comments
{
    public class DeleteModel : PageModel
    {
        private readonly ICommentService _commentService;

        public DeleteModel(ICommentService commentService)
        {
            _commentService = commentService;
        }

        [BindProperty]
        public Comment Comment { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            Comment = await _commentService.GetByIdAsync(id);

            if (Comment == null)
            {
                return NotFound();
            }
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            await _commentService.DeleteAsync(Comment.CommentId);

            TempData["Message"] = "Xóa bình luận thành công";
            return RedirectToPage("Index");
        }
    }
}