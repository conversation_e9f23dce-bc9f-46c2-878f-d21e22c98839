﻿@page "{id:int}"
@model CLanWork.Web.Pages.Users.EditModel
@{
    ViewData["Title"] = "Chỉnh sửa người dùng";
}

<h2>Chỉnh sửa người dùng</h2>

<form method="post" id="editForm">
    <input type="hidden" asp-for="User.UserId" />

    <div asp-validation-summary="All" class="text-danger"></div>

    <div class="form-group">
        <label asp-for="User.Username"></label>
        <input asp-for="User.Username" class="form-control" />
        <span asp-validation-for="User.Username" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="User.Email"></label>
        <input asp-for="User.Email" class="form-control" />
        <span asp-validation-for="User.Email" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="User.Password"></label>
        <input asp-for="User.Password" class="form-control" type="password" />
        <span asp-validation-for="User.Password" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="User.Role"></label>
        <select asp-for="User.Role" asp-items="Model.RolesOptions" class="form-control"></select>
        <span asp-validation-for="User.Role" class="text-danger"></span>
    </div>

    <button type="submit" class="btn btn-primary">Lưu thay đổi</button>
    <a asp-page="Index" class="btn btn-secondary">Hủy</a>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}