using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CLanWork.Data.Entities;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.AI.Interfaces
{
    public interface IAIReviewResultService
    {
        /// <summary>
        /// Tạo kết quả đánh giá mới bởi AI cho một task.
        /// </summary>
        /// <param name="result">Thông tin kết quả cần tạo</param>
        /// <returns>Kết quả đã được tạo</returns>
        Task<AIReviewResult> CreateAsync(AIReviewResult result);

        /// <summary>
        /// Lấy kết quả đánh giá theo ID.
        /// </summary>
        /// <param name="id">ID của kết quả</param>
        /// <returns>Kết quả đánh giá hoặc null nếu không tồn tại</returns>
        Task<AIReviewResult?> GetByIdAsync(int id);

        /// <summary>
        /// Lấy tất cả kết quả đánh giá.
        /// </summary>
        /// <returns>Danh sách kết quả</returns>
        Task<List<AIReviewResult>> GetAllAsync();

        /// <summary>
        /// Lấy danh sách kết quả đánh giá theo TaskId.
        /// </summary>
        /// <param name="taskId">ID của task</param>
        /// <returns>Danh sách kết quả đánh giá của task đó</returns>
        Task<List<AIReviewResult>> GetByTaskIdAsync(int taskId);

        /// <summary>
        /// Cập nhật kết quả đánh giá.
        /// </summary>
        /// <param name="result">Dữ liệu mới cần cập nhật</param>
        Task UpdateAsync(AIReviewResult result);

        /// <summary>
        /// Xóa kết quả đánh giá theo ID.
        /// </summary>
        /// <param name="id">ID cần xóa</param>
        Task DeleteAsync(int id);
    }
}