using CLanWork.AI.Interfaces;
using CLanWork.Data;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;

namespace CLanWork.Web.Pages.Users
{
    public class DetailsModel : PageModel
    {
        private readonly IUserService _userService;

        public DetailsModel(IUserService userService)
        {
            _userService = userService;
        }

        public User User { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            User = await _userService.GetByIdAsync(id);

            if (User == null)
            {
                return NotFound();
            }

            return Page();
        }
    }
}
