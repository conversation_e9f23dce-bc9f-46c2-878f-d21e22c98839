using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using CLanWork.Data.Interfaces;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.AI.Services
{
    public class ClubService : IClubService
    {
        private readonly IClubRepository _repository;

        public ClubService(IClubRepository repository)
        {
            _repository = repository;
        }

        public async Task<Club> CreateAsync(Club club)
        {
            return await _repository.CreateAsync(club);
        }

        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id);
        }

        public async Task<List<Club>> GetAllAsync()
        {
            return await _repository.GetAllAsync();
        }

        public async Task<Club?> GetByIdAsync(int id)
        {
            return await _repository.GetByIdAsync(id);
        }

        public async Task<List<Club>> GetByUserIdAsync(int userId)
        {
            return await _repository.GetByUserIdAsync(userId);
        }

        public async Task UpdateAsync(Club club)
        {
            await _repository.UpdateAsync(club);
        }
    }
}