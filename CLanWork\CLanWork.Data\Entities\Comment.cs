using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace CLanWork.Data.Entities;

public class Comment
{
    public int CommentId { get; set; }
    public int TaskId { get; set; }
    public int UserId { get; set; }
    [Required (ErrorMessage = "Nội dung bình luận không được để trống")]
    public string Content { get; set; }
    [Required (ErrorMessage = "<PERSON><PERSON>y tạo không được để trống")]
    public DateTime CreatedAt { get; set; }
    [ValidateNever]
    public Task Task { get; set; }
    [ValidateNever]
    public User User { get; set; }
}
