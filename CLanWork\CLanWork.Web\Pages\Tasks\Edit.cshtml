﻿@page "{id:int}"
@model CLanWork.Web.Pages.Tasks.EditModel
@{
    ViewData["Title"] = "Chỉnh sửa Task";
}

<h2>Chỉnh sửa Task</h2>

<form method="post">
    <input type="hidden" asp-for="Task.TaskId" />

    <div class="form-group">
        <label asp-for="Task.Title"></label>
        <input asp-for="Task.Title" class="form-control" />
        <span asp-validation-for="Task.Title" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="Task.Description"></label>
        <textarea asp-for="Task.Description" class="form-control"></textarea>
    </div>

    <div class="form-group">
        <label asp-for="Task.DueDate"></label>
        <input asp-for="Task.DueDate" class="form-control" type="date" />
        <span asp-validation-for="Task.DueDate" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="Task.Priority"></label>
        <select asp-for="Task.Priority" asp-items="Model.PriorityOptions" class="form-control"></select>
        <span asp-validation-for="Task.Priority" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="Task.Status"></label>
        <select asp-for="Task.Status" asp-items="Model.StatusOptions" class="form-control"></select>
        <span asp-validation-for="Task.Status" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="Task.ClubId"></label>
        <select asp-for="Task.ClubId" asp-items="Model.ClubOptions" class="form-control"></select>
        <span asp-validation-for="Task.ClubId" class="text-danger"></span>
    </div>

    <button type="submit" class="btn btn-primary">Lưu thay đổi</button>
    <a asp-page="Index" class="btn btn-secondary">Huỷ</a>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}