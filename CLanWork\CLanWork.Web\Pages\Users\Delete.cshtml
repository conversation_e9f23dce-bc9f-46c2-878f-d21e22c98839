﻿@page "{id:int}"
@model CLanWork.Web.Pages.Users.DeleteModel
@{
    ViewData["Title"] = "Xóa người dùng";
}

<h2 class="mb-4 text-danger">X<PERSON><PERSON> nhận xóa người dùng</h2>

<div class="card border-danger">
    <div class="card-body">
        <p>Bạn có chắc chắn muốn xóa người dùng sau không?</p>

        <table class="table table-sm table-borderless">
            <tbody>
                <tr>
                    <th style="width: 150px;">Username:</th>
                    <td>@Model.User.Username</td>
                </tr>

                <tr>
                    <th>Email:</th>
                    <td>@Model.User.Email</td>
                </tr>

                <tr>
                    <th>Role:</th>
                    <td>@Model.User.Role</td>
                </tr>
            </tbody>
        </table>

        <form method="post">
            <input type="hidden" asp-for="User.UserId" />
            <button type="submit" class="btn btn-danger me-2">X<PERSON><PERSON> nhận xóa</button>
            <a asp-page="Index" class="btn btn-secondary">Hủy</a>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}