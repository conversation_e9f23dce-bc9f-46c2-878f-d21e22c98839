using CLanWork.Data.Entities;
using CLanWork.Data.Interfaces;
using Microsoft.EntityFrameworkCore;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Data.Repositories
{
    public class CommentRepository : ICommentRepository
    {
        private readonly CLanWorkContext _context;

        public CommentRepository(CLanWorkContext context)
        {
            _context = context;
        }

        public async Task<Comment> CreateAsync(Comment comment)
        {
            int maxId = await _context.Comments.AnyAsync()
                ? await _context.Comments.MaxAsync(c => c.CommentId)
                : 0;

            comment.CommentId = maxId + 1;

            _context.Comments.Add(comment);
            await _context.SaveChangesAsync();
            return comment;
        }

        public async Task DeleteAsync(int id)
        {
            var comment = await _context.Comments.FindAsync(id);

            if (comment != null)
            {
                _context.Comments.Remove(comment);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<List<Comment>> GetAllAsync()
        {
            return await _context.Comments
                .Include(c => c.User)
                .Include(c => c.Task)
                .ToListAsync();
        }

        public async Task<Comment?> GetByIdAsync(int id)
        {
            return await _context.Comments.Include(c => c.User)
                                            .Include(c => c.Task)
                                            .FirstOrDefaultAsync(c => c.CommentId == id);
        }

        public async Task<List<Comment>> GetByTaskIdAsync(int taskId)
        {
            return await _context.Comments
                .Where(c => c.TaskId == taskId)
                .Include(c => c.User)
                .ToListAsync();
        }

        public async Task UpdateAsync(Comment comment)
        {
            _context.Comments.Update(comment);
            await _context.SaveChangesAsync();
        }
    }
}