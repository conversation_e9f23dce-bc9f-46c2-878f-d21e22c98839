using System.ComponentModel.DataAnnotations;

namespace CLanWork.Data.Entities;

public class Club
{
    public int ClubId { get; set; }

    [Required(ErrorMessage = "Tên CLB là bắt buộc")]
    public string Name { get; set; }
    public string? Description { get; set; }

    public ICollection<Member> Members { get; set; } = new List<Member>();
    public ICollection<Task>? Tasks { get; set; } = new List<Task>();
}
