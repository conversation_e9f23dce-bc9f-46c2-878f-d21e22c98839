﻿@page
@model CLanWork.Web.Pages.Tasks.IndexModel
@{
    ViewData["Title"] = "Task List";
}

<h2>Danh sách Task</h2>

<p>
    <a class="btn btn-primary" asp-page="Create">+ Tạo Task mới</a>
</p>

<table class="table table-striped">
    <thead>
        <tr>
            <th>Title</th>
            <th>Deadline</th>
            <th>Status</th>
            <th>Actions</th>
        </tr>
    </thead>

    <tbody>
        @foreach (var task in Model.Tasks)
        {
            <tr>
                <td>@task.Title</td>
                <td>@(task.DueDate?.ToString("dd/MM/yyyy") ?? "Chưa có")</td>
                <td>@task.Status</td>
                
                <td>
                    <a class="btn btn-sm btn-info" asp-page="Review" asp-route-id="@task.TaskId">Review</a>
                    <a class="btn btn-sm btn-warning" asp-page="Edit" asp-route-id="@task.TaskId">Sửa</a>
                    <a class="btn btn-sm btn-danger" asp-page="Delete" asp-route-id="@task.TaskId">Xoá</a>
                </td>
            </tr>
        }
    </tbody>
</table>

@if (Model.Tasks.Count == 0)
{
    <tr>
        <td colspan="4" class="text-center">Không có task nào.</td>
    </tr>
}

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}