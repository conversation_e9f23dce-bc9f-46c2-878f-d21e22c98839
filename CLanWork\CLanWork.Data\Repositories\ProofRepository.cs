using CLanWork.Data.Entities;
using CLanWork.Data.Interfaces;
using Microsoft.EntityFrameworkCore;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Data.Repositories
{
    public class ProofRepository : IProofRepository
    {
        private readonly CLanWorkContext _context;

        public ProofRepository(CLanWorkContext context)
        {
            _context = context;
        }

        public async Task ApproveAsync(int proofId)
        {
            var proof = await _context.Proofs.FindAsync(proofId);

            if (proof != null)
            {
                proof.Approved = true;
                _context.Proofs.Update(proof);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<Proof> CreateAsync(Proof proof)
        {
            int maxId = await _context.Proofs.AnyAsync()
                ? await _context.Proofs.MaxAsync(p => p.ProofId)
                : 0;

            proof.ProofId = maxId + 1;
            _context.Proofs.Add(proof);
            await _context.SaveChangesAsync();
            return proof;
        }

        public async Task DeleteAsync(int id)
        {
            var proof = await _context.Proofs.FindAsync(id);

            if (proof != null)
            {
                _context.Proofs.Remove(proof);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<List<Proof>> GetAllAsync()
        {
            return await _context.Proofs
                .Include(p => p.Task)
                .ToListAsync();
        }

        public async Task<Proof?> GetByIdAsync(int id)
        {
            return await _context.Proofs
                .Include(p => p.Task)
                .FirstOrDefaultAsync(p => p.ProofId == id);
        }

        public async Task<List<Proof>> GetByTaskIdAsync(int taskId)
        {
            return await _context.Proofs
                .Where(p => p.TaskId == taskId)
                .Include(p => p.Task)
                .ToListAsync();
        }

        public async Task UpdateAsync(Proof proof)
        {
            _context.Proofs.Update(proof);
            await _context.SaveChangesAsync();
        }
    }
}