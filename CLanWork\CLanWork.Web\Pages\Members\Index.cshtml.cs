using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Web.Pages.Members
{
    public class IndexModel : PageModel
    {
        private readonly IMemberService _memberService;

        public IndexModel(IMemberService memberService)
        {
            _memberService = memberService;
        }

        public IList<Member> Members { get; set; }

        public async Task OnGetAsync()
        {
            Members = await _memberService.GetAllAsync();
        }
    }
}