using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using CLanWork.Data;
using CLanWork.Data.Entities;
using CLanWork.AI.Interfaces;

namespace CLanWork.Web.Pages.Clubs;

public class EditModel : PageModel
{
    private readonly IClubService _clubService;

    public EditModel(IClubService clubService)
    {
        _clubService = clubService;
    }

    [BindProperty]
    public Club Club { get; set; } = new();

    public async Task<IActionResult> OnGetAsync(int id)
    {
        Club = await _clubService.GetByIdAsync(id);
        return Club == null ? NotFound() : Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        if (!ModelState.IsValid) return Page();

        try
        {
            await _clubService.UpdateAsync(Club);
            TempData["SuccessMessage"] = "Club updated successfully.";
        }
        catch (Exception ex)
        {

            ModelState.AddModelError("", $"Lỗi cập nhật: {ex.Message}");
        }
        
        return RedirectToPage("Index");
    }
}