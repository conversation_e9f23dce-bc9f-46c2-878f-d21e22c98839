using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using EntityTask = CLanWork.Data.Entities.Task;
using CLanWork.AI.Interfaces;
using Microsoft.AspNetCore.Authorization;

namespace CLanWork.Web.Pages.Tasks
{
    // [Authorize(Roles = "Admin, ClubAdmin")]
    public class CreateModel : PageModel
    {
        private readonly ITaskService _taskService;
        private readonly IClubService _clubService;

        public CreateModel(ITaskService service, IClubService clubService)
        {
            _taskService = service;
            _clubService = clubService;
        }

        [BindProperty]
        public EntityTask Task { get; set; }

        public SelectList ClubOptions { get; set; }

        public SelectList PriorityOptions { get; set; }

        public SelectList StatusOptions { get; set; }

        private async Task LoadOptions()
        {
            var clubs = await _clubService.GetAllAsync();

            PriorityOptions = new SelectList(new[] { "Low", "Medium", "High" });
            StatusOptions = new SelectList(new[] { "Todo", "InProgress", "Done" });
            ClubOptions = new SelectList(clubs, "ClubId", "Name");
        }

        public async Task<IActionResult> OnGetAsync()
        {
            await LoadOptions();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadOptions();

                foreach (var modelState in ModelState)
                {
                    foreach (var error in modelState.Value.Errors)
                    {
                        Console.WriteLine($"Model error in {modelState.Key}: {error.ErrorMessage}");
                    }
                }

                return Page();
            }

            await _taskService.CreateAsync(Task);

            TempData["SuccessMessage"] = "Task created successfully.";
            return RedirectToPage("Index");
        }
    }
}
