using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using CLanWork.Data.Interfaces;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.AI.Services
{
    public class ProofService : IProofService
    {
        private readonly IProofRepository _repository;

        public ProofService(IProofRepository repository)
        {
            _repository = repository;
        }

        public async Task ApproveAsync(int proofId)
        {
            await _repository.ApproveAsync(proofId);
        }

        public async Task<Proof> CreateAsync(Proof proof)
        {
            return await _repository.CreateAsync(proof);
        }

        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id);
        }

        public async Task<List<Proof>> GetAllAsync()
        {
            return await _repository.GetAllAsync();
        }

        public async Task<Proof?> GetByIdAsync(int id)
        {
            return await _repository.GetByIdAsync(id);
        }

        public async Task<List<Proof>> GetByTaskIdAsync(int taskId)
        {
            return await _repository.GetByTaskIdAsync(taskId);
        }

        public async Task UpdateAsync(Proof proof)
        {
            await _repository.UpdateAsync(proof);
        }
    }
}