﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - CLanWork</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" />
    <link rel="stylesheet" href="~/css/landing.css" />
</head>

<body class="d-flex flex-column min-vh-100">

    <!-- Nội dung hiển thị đầy đủ chiều ngang -->
    <main role="main" class="flex-grow-1 w-100">
        @RenderBody()
    </main>

    <!-- Footer -->
    <footer class="footer">
        © 2025 CLanWork. Một sản phẩm của nhóm SWD392 - FPT University.
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        window.addEventListener('scroll', function () {
            const header = document.getElementById('clan-header');
            if (window.scrollY > 50) {
                header?.classList.add('scrolled');
            } else {
                header?.classList.remove('scrolled');
            }
        });
    </script>

    @RenderSection("Scripts", required: false)
</body>
</html>
