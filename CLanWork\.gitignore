## Build results
bin/
obj/
*.exe
*.dll
*.pdb

## User-specific files
*.user
*.suo
*.userosscache
*.sln.docstates

## Rider & VS Code
.idea/
.vscode/

## ASP.NET Core Secrets
secrets.json

## .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

## Logs
*.log

## NuGet
*.nupkg
packages/
.nuget/

## VS Backup
*.bak

## OS junk files
.DS_Store
Thumbs.db
# Temporary files
*.tmp
# Visual Studio Code workspace storage
.vscode/.history/
# Visual Studio Code settings
.vscode/settings.json
# Visual Studio Code extensions
.vscode/extensions.json
# Visual Studio Code workspace storage
.vscode/workspaceStorage/
# Visual Studio Code tasks
.vscode/tasks.json
# Visual Studio Code launch configurations
.vscode/launch.json
# Visual Studio Code workspace state
.vscode/workspaceState.json