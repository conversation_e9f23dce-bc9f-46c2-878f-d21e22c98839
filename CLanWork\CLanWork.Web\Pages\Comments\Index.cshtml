@page
@model CLanWork.Web.Pages.Comments.IndexModel
@{
    ViewData["Title"] = "Danh sách Comment";
}

<h2>Danh sách Comment</h2>

<p>
    <a asp-page="Create" class="btn btn-primary">Thêm Comment</a>
</p>

<table class="table">
    <thead>
        <tr>
            <th>Nội dung</th>
            <th>Người đăng</th>
            <th>Task</th>
            <th>Thời gian</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model.Comments)
        {
            <tr>
                <td>@item.Content</td>
                <td>@item.User?.Username</td>
                <td>@item.Task?.Title</td>
                <td>@item.CreatedAt.ToString("dd/MM/yyyy HH:mm")</td>
                <td>
                    <a asp-page="Edit" asp-route-id="@item.CommentId">Sửa</a> |
                    <a asp-page="Delete" asp-route-id="@item.CommentId">Xóa</a>
                </td>
            </tr>
        }
    </tbody>
</table>
