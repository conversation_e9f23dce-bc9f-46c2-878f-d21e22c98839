using CLanWork.Data.Entities;
using CLanWork.Data.Interfaces;
using Microsoft.EntityFrameworkCore;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Data.Repositories
{
    public class TaskAssigneeRepository : ITaskAssigneeRepository
    {
        private readonly CLanWorkContext _context;

        public TaskAssigneeRepository(CLanWorkContext context)
        {
            _context = context;
        }

        public async Task<TaskAssignee> CreateAs<PERSON>(TaskAssignee assignee)
        {
            int maxId = await _context.TaskAssignees.AnyAsync()
                ? await _context.TaskAssignees.MaxAsync(a => a.Id)
                : 0;

            assignee.Id = maxId + 1;
            _context.TaskAssignees.Add(assignee);
            await _context.SaveChangesAsync();
            return assignee;
        }

        public async Task DeleteAsync(int id)
        {
            var assignee = await _context.TaskAssignees.FindAsync(id);

            if (assignee != null)
            {
                _context.TaskAssignees.Remove(assignee);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<List<TaskAssignee>> GetAllAsync()
        {
            return await _context.TaskAssignees
                .Include(a => a.Task)
                .Include(a => a.User)
                .ToListAsync();
        }

        public async Task<List<User>> GetAssigneesByTaskIdAsync(int taskId)
        {
            return await _context.TaskAssignees
                .Where(a => a.TaskId == taskId)
                .Include(a => a.User)
                .Select(a => a.User)
                .ToListAsync();
        }

        public async Task<TaskAssignee?> GetByIdAsync(int id)
        {
            return await _context.TaskAssignees
                .Include(a => a.Task)
                .Include(a => a.User)
                .FirstOrDefaultAsync(a => a.Id == id);
        }

        public async Task<List<Entities.Task>> GetTasksByUserIdAsync(int userId)
        {
            return await _context.TaskAssignees
                .Where(a => a.UserId == userId)
                .Include(a => a.Task)
                .Select(a => a.Task)
                .ToListAsync();
        }

        public async Task UpdateAsync(TaskAssignee assignee)
        {
            _context.TaskAssignees.Update(assignee);
            await _context.SaveChangesAsync();
        }
    }
}