using CLanWork.Data.Entities;
using CLanWork.Data.Interfaces;
using Microsoft.EntityFrameworkCore;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Data.Repositories
{
    public class AIReviewResultRepository : IAIReviewResultRepository
    {
        private readonly CLanWorkContext _context;

        public AIReviewResultRepository(CLanWorkContext context)
        {
            _context = context;
        }

        public async Task<AIReviewResult> CreateAsync(AIReviewResult result)
        {
            int maxId = await _context.AIReviewResults.AnyAsync()
                ? await _context.AIReviewResults.MaxAsync(r => r.Id)
                : 0;

            result.Id = maxId + 1;
            _context.AIReviewResults.Add(result);
            await _context.SaveChangesAsync();
            return result;
        }

        public async System.Threading.Tasks.Task DeleteAsync(int id)
        {
            var result = await _context.AIReviewResults.FindAsync(id);

            if (result != null)
            {
                _context.AIReviewResults.Remove(result);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<List<AIReviewResult>> GetAllAsync()
        {
            return await _context.AIReviewResults
                .Include(r => r.Task)
                .ToListAsync();
        }

        public async Task<AIReviewResult?> GetByIdAsync(int id)
        {
            return await _context.AIReviewResults
                .Include(r => r.Task)
                .FirstOrDefaultAsync(r => r.Id == id);
        }

        public async Task<List<AIReviewResult>> GetByTaskIdAsync(int taskId)
        {
            return await _context.AIReviewResults
                .Where(r => r.TaskId == taskId)
                .Include(r => r.Task)
                .ToListAsync();
        }

        public async Task UpdateAsync(AIReviewResult result)
        {
            _context.AIReviewResults.Update(result);
            await _context.SaveChangesAsync();
        }
    }
}