using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace CLanWork.Web.Pages.TaskAssignees
{
    public class DetailsModel : PageModel
    {
        private readonly ITaskAssigneeService _taskAssigneeService;

        public DetailsModel(ITaskAssigneeService taskAssigneeService)
        {
            _taskAssigneeService = taskAssigneeService;
        }

        public TaskAssignee TaskAssignee { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            TaskAssignee = await _taskAssigneeService.GetByIdAsync(id);

            if (TaskAssignee == null)
                return NotFound();

            return Page();
        }

    }
}