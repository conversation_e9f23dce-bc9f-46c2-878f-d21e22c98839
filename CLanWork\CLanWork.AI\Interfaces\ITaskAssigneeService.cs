using CLanWork.Data.Entities;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.AI.Interfaces
{
    public interface ITaskAssigneeService
    {
        Task<TaskAssignee> CreateAsync(TaskAssignee assignee);
        Task<TaskAssignee?> GetByIdAsync(int id);
        Task<List<TaskAssignee>> GetAllAsync();
        Task<List<User>> GetAssigneesByTaskIdAsync(int taskId);
        Task<List<Data.Entities.Task>> GetTasksByUserIdAsync(int userId);
        Task UpdateAsync(TaskAssignee assignee);
        Task DeleteAsync(int id);
    }
}