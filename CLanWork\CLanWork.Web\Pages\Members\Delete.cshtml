@page "{id:int}"
@model CLanWork.Web.Pages.Members.DeleteModel
@{
    ViewData["Title"] = "Xóa Member";
}

<h2>Xóa Member</h2>

<h3><PERSON><PERSON><PERSON> c<PERSON> chắc chắn muốn xóa Member n<PERSON><PERSON>h<PERSON>?</h3>

<div>
    <dl class="row">
        <dt class="col-sm-2">ID</dt>
        <dd class="col-sm-10">@Model.Member.MemberId</dd>

        <dt class="col-sm-2">User</dt>
        <dd class="col-sm-10">@Model.Member.User?.Username</dd>

        <dt class="col-sm-2">Club</dt>
        <dd class="col-sm-10">@Model.Member.Club?.Name</dd>

        <dt class="col-sm-2">Role</dt>
        <dd class="col-sm-10">@Model.Member.Role</dd>
    </dl>

    <form method="post">
        <input type="hidden" asp-for="Member.MemberId" />
        <button type="submit" class="btn btn-danger">Xóa</button>
        <a asp-page="Index" class="btn btn-secondary ms-2">Hủy</a>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}