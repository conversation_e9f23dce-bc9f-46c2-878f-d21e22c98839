using EntityTask = CLanWork.Data.Entities.Task;

namespace CLanWork.Data.Interfaces
{
    public interface ITaskRepository
    {
        Task<EntityTask> CreateAsync(EntityTask task);
        Task<EntityTask?> GetByIdAsync(int id);
        Task<List<EntityTask>> GetAllAsync();
        Task<List<EntityTask>> GetByClubIdAsync(int clubId);
        Task UpdateAsync(EntityTask task);
        Task DeleteAsync(int id);
        Task<List<EntityTask>> GetUpcomingTasksAsync(DateTime from, DateTime to);
    }
}