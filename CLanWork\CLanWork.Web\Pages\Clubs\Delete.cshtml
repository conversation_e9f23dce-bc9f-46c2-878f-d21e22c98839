﻿@page "{id:int}"
@model CLanWork.Web.Pages.Clubs.DeleteModel
@{
    ViewData["Title"] = "Xoá CLB";
}

<h2>Bạn có chắc muốn xoá CLB này?</h2>

<div>
    <h4>@Model.Club.Name</h4>
    <p>@Model.Club.Description</p>
</div>

<form method="post">
    <input type="hidden" asp-for="Club.ClubId" />
    <button type="submit" class="btn btn-danger">Xoá</button>
    <a asp-page="Index" class="btn btn-secondary">Huỷ</a>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}