using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Web.Pages.Comments
{
    public class IndexModel : PageModel
    {
        private readonly ICommentService _commentService;

        public IndexModel(ICommentService commentService)
        {
            _commentService = commentService;
        }

        public IList<Comment> Comments { get; set; }

        public async Task OnGetAsync()
        {
            Comments = await _commentService.GetAllAsync();
        }
    }
}