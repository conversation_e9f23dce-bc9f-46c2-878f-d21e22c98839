@page
@model CLanWork.Web.Pages.Members.IndexModel
@{
    ViewData["Title"] = "Danh sách Member";
}

<h2><PERSON>h sách Member</h2>

<p>
    <a asp-page="Create" class="btn btn-primary">Tạo Member M<PERSON>i</a>
</p>

<table class="table table-striped">
    <thead>
        <tr>
            <th>ID</th>
            <th>User</th>
            <th>Club</th>
            <th>Role</th>
            <th>Hành động</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var member in Model.Members)
        {
            <tr>
                <td>@member.MemberId</td>
                <td>@member.User?.Username</td>
                <td>@member.Club?.Name</td>
                <td>@member.Role</td>
                <td>
                    <a asp-page="Details" asp-route-id="@member.MemberId">Xem</a> |
                    <a asp-page="Edit" asp-route-id="@member.MemberId">Sửa</a> |
                    <a asp-page="Delete" asp-route-id="@member.MemberId">Xóa</a>
                </td>
            </tr>
        }
    </tbody>
</table>
