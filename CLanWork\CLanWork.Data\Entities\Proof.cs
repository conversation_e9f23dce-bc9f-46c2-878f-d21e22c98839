using System.ComponentModel.DataAnnotations;

namespace CLanWork.Data.Entities;

public class Proof
{
    public int ProofId { get; set; }
    public int TaskId { get; set; }
    [Required(ErrorMessage = "File là bắt buộc.")]
    public string FileUrl { get; set; }
    [Required(ErrorMessage = "Ngày tải lên là bắt buộc.")]
    public DateTime UploadDate { get; set; }
    [Required(ErrorMessage = "Trạng thái là bắt buộc.")]
    public bool Approved { get; set; }
    public Task Task { get; set; }
}
