using CLanWork.Data.Entities;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Data.Interfaces
{
    public interface IAIReviewResultRepository
    {
        Task<AIReviewResult> CreateAsync(AIReviewResult result);
        Task<AIReviewResult?> GetByIdAsync(int id);
        Task<List<AIReviewResult>> GetAllAsync();
        Task<List<AIReviewResult>> GetByTaskIdAsync(int taskId);
        Task UpdateAsync(AIReviewResult result);
        Task DeleteAsync(int id);
    }
}