using System.ComponentModel.DataAnnotations;

namespace CLanWork.Data.Entities;

public class Task
{
    public int TaskId { get; set; }
    [Required]
    public int ClubId { get; set; }
    [Required(ErrorMessage = "Tiêu đề công việc là bắt buộc")]
    public string Title { get; set; }
    public string? Description { get; set; }
    [Required]
    public string Status { get; set; }
    [Required]
    public string Priority { get; set; }
    [Required(ErrorMessage = "Hạn là bắt buộc")]
    [DataType(DataType.Date)]
    public DateTime? DueDate { get; set; }

    public Club? Club { get; set; }
    public ICollection<Comment> Comments { get; set; } = new List<Comment>();
    public ICollection<TaskAssignee> Assignees { get; set; } = new List<TaskAssignee>();
    public ICollection<Proof> Proofs { get; set; } = new List<Proof>();
}
