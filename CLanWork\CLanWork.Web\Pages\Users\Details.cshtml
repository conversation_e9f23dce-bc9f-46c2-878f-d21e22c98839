﻿@page "{id:int}"
@model CLanWork.Web.Pages.Users.DetailsModel
@{
    ViewData["Title"] = "Chi tiết người dùng";
}

<h2 class="mb-4">Chi tiết người dùng</h2>

<div class="card shadow-sm">
    <div class="card-body">
        <table class="table table-borderless mb-0">
            <tbody>
                <tr>
                    <th scope="row">Username</th>
                    <td>@Model.User.Username</td>
                </tr>

                <tr>
                    <th scope="row">Email</th>
                    <td>@Model.User.Email</td>
                </tr>

                <tr>
                    <th scope="row">Password</th>
                    <td>@Model.User.Password</td>
                </tr>

                <tr>
                    <th scope="row">Role</th>
                    <td>@Model.User.Role</td>
                </tr>

                <tr>
                    <th scope="row">Số lượng CLB tham gia</th>
                    <td>@(Model.User.Memberships?.Count() ?? 0)</td>
                </tr>

                <tr>
                    <th scope="row">Số lượng nhiệm vụ được giao</th>
                    <td>@(Model.User.AssignedTasks?.Count() ?? 0)</td>
                </tr>

                <tr>
                    <th scope="row">Số lượng bình luận</th>
                    <td>@(Model.User.Comments?.Count() ?? 0)</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<div class="mt-4">
    <a asp-page="Edit" asp-route-id="@Model.User.UserId" class="btn btn-primary me-2">Sửa</a>
    <a asp-page="Index" class="btn btn-secondary">Quay lại</a>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}