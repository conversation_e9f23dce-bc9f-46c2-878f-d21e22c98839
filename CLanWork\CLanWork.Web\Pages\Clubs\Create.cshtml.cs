using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using CLanWork.Data.Entities;
using CLanWork.AI.Interfaces;

namespace CLanWork.Web.Pages.Clubs;

public class CreateModel : PageModel
{
    private readonly IClubService _clubService;

    public CreateModel(IClubService clubService)
    {
        _clubService = clubService;
    }

    [BindProperty]
    public Club Club { get; set; } = new();

    public IActionResult OnGet() => Page();

    public async Task<IActionResult> OnPostAsync()
    {
        if (!ModelState.IsValid)
            return Page();

        await _clubService.CreateAsync(Club);

                    TempData["SuccessMessage"] = "Club created successfully.";
        return RedirectToPage("Index");
    }
}