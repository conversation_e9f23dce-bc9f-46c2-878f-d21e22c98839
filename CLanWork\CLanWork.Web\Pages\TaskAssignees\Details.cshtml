@page
@model CLanWork.Web.Pages.TaskAssignees.DetailsModel
@{
    ViewData["Title"] = "Task Assignee Details";
}

<h2>Task Assignee Details</h2>

<div>
    <h4>Task Assignee</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">ID</dt>
        <dd class="col-sm-10">@Model.TaskAssignee.Id</dd>

        <dt class="col-sm-2">Task</dt>
        <dd class="col-sm-10">@Model.TaskAssignee.Task?.Title</dd>

        <dt class="col-sm-2">User</dt>
        <dd class="col-sm-10">@Model.TaskAssignee.User?.Username</dd>
    </dl>
    <a asp-page="Index" class="btn btn-primary">Back to List</a>
</div>

