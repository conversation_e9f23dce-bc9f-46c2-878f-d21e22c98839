using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using CLanWork.Data.Interfaces;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.AI.Services
{
    public class MemberService : IMemberService
    {
        private readonly IMemberRepository _repository;

        public MemberService(IMemberRepository repository)
        {
            _repository = repository;
        }

        public async Task<Member> CreateAsync(Member member)
        {
            return await _repository.CreateAsync(member);
        }

        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id);
        }

        public async Task<List<Member>> GetAllAsync()
        {
            return await _repository.GetAllAsync();
        }

        public async Task<List<Member>> GetByClubIdAsync(int clubId)
        {
            return await _repository.GetByClubIdAsync(clubId);
        }

        public async Task<Member?> GetByIdAsync(int id)
        {
            return await _repository.GetByIdAsync(id);
        }

        public async Task<List<Member>> GetByUserIdAsync(int userId)
        {
            return await _repository.GetByUserIdAsync(userId);
        }

        public async Task UpdateAsync(Member member)
        {
            await _repository.UpdateAsync(member);
        }
    }
}