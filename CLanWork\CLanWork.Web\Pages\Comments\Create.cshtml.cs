using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Task = System.Threading.Tasks.Task;

namespace CLanWork.Web.Pages.Comments
{
    public class CreateModel : PageModel
    {
        private readonly ICommentService _commentService;
        private readonly IUserService _userService;
        private readonly ITaskService _taskService;

        public CreateModel(ICommentService commentService, IUserService userService, ITaskService taskService)
        {
            _commentService = commentService;
            _userService = userService;
            _taskService = taskService;
        }

        [BindProperty]
        public Comment Comment { get; set; }

        public SelectList TaskOptions { get; set; }
        public SelectList UserOptions { get; set; }

        private async Task LoadOptions()
        {
            TaskOptions = new SelectList(await _taskService.GetAllAsync(), "TaskId", "Title");
            UserOptions = new SelectList(await _userService.GetAllAsync(), "UserId", "Username");
        }

        public async Task<IActionResult> OnGetAsync()
        {
            await LoadOptions();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            Comment.CreatedAt = DateTime.Now;

            if (!ModelState.IsValid)
            {
                await LoadOptions(); // Đảm bảo không lỗi asp-items
                
                TempData["ErrorMessage"] = "Vui lòng kiểm tra lại thông tin đã nhập.";
                return Page();
            }

            try
            {

                await _commentService.CreateAsync(Comment);

                TempData["SuccessMessage"] = "Comment created successfully!";
                return RedirectToPage("Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, $"Lỗi khi lưu comment: {ex.Message}");
                await LoadOptions(); // reload lại dropdown
                return Page();
            }
        }
    }
}
