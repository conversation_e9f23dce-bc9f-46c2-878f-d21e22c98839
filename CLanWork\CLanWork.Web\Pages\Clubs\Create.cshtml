﻿@page
@model CLanWork.Web.Pages.Clubs.CreateModel
@{
    ViewData["Title"] = "Tạo CLB mới";
}

<h2>Tạo Câu lạc bộ mới</h2>

<form method="post">
    <div class="form-group">
        <label asp-for="Club.Name"></label>
        <input asp-for="Club.Name" class="form-control" />
        <span asp-validation-for="Club.Name" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="Club.Description"></label>
        <textarea asp-for="Club.Description" class="form-control"></textarea>
    </div>

    <button type="submit" class="btn btn-success">Tạo</button>
    <a asp-page="Index" class="btn btn-secondary">Huỷ</a>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}