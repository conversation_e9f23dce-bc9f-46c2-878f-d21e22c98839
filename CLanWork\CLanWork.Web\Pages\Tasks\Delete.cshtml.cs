using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using CLanWork.Data;
using EntityTask = CLanWork.Data.Entities.Task;
using CLanWork.AI.Interfaces;

namespace CLanWork.Web.Pages.Tasks;

public class DeleteModel : PageModel
{
    private readonly ITaskService _taskService;


    public DeleteModel(ITaskService taskService)
    {
        _taskService = taskService;
    }

    [BindProperty]
    public EntityTask Task { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        Task = await _taskService.GetByIdAsync(id);

        if (Task == null)
            return NotFound();

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        if (Task == null || Task.TaskId == 0)
        {
            return NotFound();
        }

        await _taskService.DeleteAsync(Task.TaskId);
        
        TempData["SuccessMessage"] = "Task deleted successfully.";
        return RedirectToPage("Index");
    }
}
