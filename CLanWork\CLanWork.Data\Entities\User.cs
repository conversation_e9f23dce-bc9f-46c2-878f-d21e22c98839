using System.ComponentModel.DataAnnotations;

namespace CLanWork.Data.Entities;

public class User
{
    public int UserId { get; set; }
    [Required(ErrorMessage = "Tên người dùng là bắt buộc")]
    public string Username { get; set; }
    [Required(ErrorMessage = "Email là bắt buộc")]
    public string Email { get; set; }
    [Required(ErrorMessage = "Mật khẩu là bắt buộc")]
    public string Password { get; set; }
    [Required(ErrorMessage = "Vai trò là bắt buộc")]
    public string Role { get; set; }

    public ICollection<Member> Memberships { get; set; } = new List<Member>();
    public ICollection<Comment> Comments { get; set; } = new List<Comment>();
    public ICollection<TaskAssignee> AssignedTasks { get; set; } = new List<TaskAssignee>();
}
