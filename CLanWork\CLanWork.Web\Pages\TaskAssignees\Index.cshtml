@page
@model CLanWork.Web.Pages.TaskAssignees.IndexModel
@{
    ViewData["Title"] = "Task Assignees";
}

<h2>Task Assignees</h2>

<p>
    <a asp-page="Create">Create New</a>
</p>

<table class="table">
    <thead>
        <tr>
            <th>ID</th>
            <th>Task</th>
            <th>User</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
    @foreach (var item in Model.TaskAssignees)
    {
        <tr>
            <td>@item.Id</td>
            <td>@item.Task?.Title</td>
            <td>@item.User?.Username</td>
            <td>
                <a asp-page="Details" asp-route-id="@item.Id">Details</a> |
                <a asp-page="Edit" asp-route-id="@item.Id">Edit</a> |
                <a asp-page="Delete" asp-route-id="@item.Id">Delete</a>
            </td>
        </tr>
    }
    </tbody>
</table>