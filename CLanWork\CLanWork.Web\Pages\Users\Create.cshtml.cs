using CLanWork.AI.Interfaces;
using CLanWork.Data;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;

namespace CLanWork.Web.Pages.Users
{
    public class CreateModel : PageModel
    {
        private readonly IUserService _userService;
        public SelectList RolesOptions { get; set; }

        public CreateModel(IUserService userService)
        {
            _userService = userService;
        }

        [BindProperty]
        public User User { get; set; }

        private void LoadRolesOptions()
        {
            RolesOptions = new SelectList(new[] { "Admin", "Club Admin", "Club Member" });
        }

        public void OnGet()
        {
            LoadRolesOptions();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                LoadRolesOptions();
                return Page();
            }

            await _userService.CreateAsync(User);
            
            TempData["SuccessMessage"] = "User created successfully.";
            return RedirectToPage("Index");
        }
    }
}
