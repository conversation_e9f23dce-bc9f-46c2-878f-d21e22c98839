@page
@model CLanWork.Web.Pages.Comments.CreateModel
@{
    ViewData["Title"] = "Tạo Comment Mới";
}

<h2 class="mb-4">Tạo Comment Mới</h2>

<form method="post">
    <div class="form-group mb-3">
        <label asp-for="Comment.Content" class="form-label">Comment</label>
        <textarea asp-for="Comment.Content" class="form-control" rows="4"></textarea>
        <span asp-validation-for="Comment.Content" class="text-danger"></span>
    </div>

    <div class="form-group mb-3">
        <label asp-for="Comment.TaskId" class="form-label">Task</label>
        <select asp-for="Comment.TaskId" class="form-control" asp-items="Model.TaskOptions"></select>
        <span asp-validation-for="Comment.TaskId" class="text-danger"></span>
    </div>

    <div class="form-group mb-3">
        <label asp-for="Comment.UserId" class="form-label">User</label>
        <select asp-for="Comment.UserId" class="form-control" asp-items="Model.UserOptions"></select>
        <span asp-validation-for="Comment.UserId" class="text-danger"></span>
    </div>

    <button type="submit" class="btn btn-primary">Tạo Comment</button>
    <a asp-page="Index" class="btn btn-secondary ms-2">Hủy</a>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}