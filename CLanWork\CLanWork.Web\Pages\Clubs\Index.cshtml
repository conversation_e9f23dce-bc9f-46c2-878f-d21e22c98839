﻿@page
@model CLanWork.Web.Pages.Clubs.IndexModel
@{
    ViewData["Title"] = "Danh sách CLB";
}

<h2><PERSON>h sách Câu lạc bộ</h2>
<p><a asp-page="Create" class="btn btn-primary">+ Tạo CLB mới</a></p>

<table class="table">
    <thead>
        <tr>
            <th>Tên CLB</th>
            <th><PERSON><PERSON> tả</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var club in Model.Clubs)
        {
            <tr>
                <td>@club.Name</td>
                <td>@club.Description</td>
                <td>
                    <a asp-page="Edit" asp-route-id="@club.ClubId" class="btn btn-sm btn-warning">Sửa</a>
                    <a asp-page="Delete" asp-route-id="@club.ClubId" class="btn btn-sm btn-danger">Xoá</a>
                </td>
            </tr>
        }
    </tbody>
</table>

@if (Model.Clubs.Count == 0)
{
    <tr>
        <td colspan="3" class="text-center">Không có câu lạc bộ nào.</td>
    </tr>
}
