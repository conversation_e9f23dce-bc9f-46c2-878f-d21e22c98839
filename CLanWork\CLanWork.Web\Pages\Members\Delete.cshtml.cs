using CLanWork.AI.Interfaces;
using CLanWork.Data.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace CLanWork.Web.Pages.Members
{
    public class DeleteModel : PageModel
    {
        private readonly IMemberService _memberService;

        public DeleteModel(IMemberService memberService)
        {
            _memberService = memberService;
        }

        [BindProperty]
        public Member Member { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            Member = await _memberService.GetByIdAsync(id);

            if (Member == null) return NotFound();

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            if (Member == null)
            {
                return NotFound();
            }

            await _memberService.DeleteAsync(id);

            TempData["SuccessMessage"] = "Member đã bị xóa!";
            return RedirectToPage("Index");
        }
    }
}