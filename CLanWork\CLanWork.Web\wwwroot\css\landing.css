body {
    font-family: 'Segoe UI', sans-serif;
    background-color: #f6fcff;
    color: #333;
    margin: 0;
}

.hero {
    position: relative;
    width: 100vw;
    min-height: 60vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: white;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('/images/background.png') center center / cover no-repeat;
    filter: blur(4px) brightness(0.5);
    z-index: 1;
}

.hero>* {
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: 48px;
    margin: 0;
}

.hero span {
    color: #00aaff;
}

.hero-wrapper {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
}

.cta-buttons {
    margin-top: 30px;
}

.cta-buttons a {
    background-color: #00aaff;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    margin: 0 10px;
    display: inline-block;
    text-decoration: none;
    font-weight: 600;
}

.section {
    padding: 60px 20px;
    max-width: 1100px;
    margin: auto;
    text-align: center;
}

.section h2 {
    color: #007acc;
    margin-bottom: 30px;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.feature {
    background: white;
    border-left: 6px solid #00aaff;
    padding: 20px;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    text-align: left;
}

.feature h3 {
    margin-top: 0;
    color: #00aaff;
}

.footer {
    background: #e0f7ff;
    padding: 30px;
    text-align: center;
    color: #555;
    margin-top: 40px;
}

/* Default: header mờ */
.clan-header {
    backdrop-filter: blur(12px);
    background-color: rgba(0, 0, 0, 0.4);
    transition: background-color 0.3s ease;
    width: 100%;
    z-index: 1030;
}

.clan-header.scrolled {
    background-color: #ffffff !important;
    backdrop-filter: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.clan-header.scrolled a,
.clan-header.scrolled span {
    color: #222 !important;
}

.btn-register {
    background-color: #00aaff;
    border: none;
    color: white;
}

.btn-register:hover {
    background-color: #0088cc;
}

.search-box input {
    background-color: white;
    border: none;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
    min-width: 200px;
}

.search-box input:focus {
    outline: none;
    box-shadow: 0 0 0 2px #00aaff;
}

.section {
    width: 100%;
    padding: 60px 20px;
}