@page "{id:int}"
@model CLanWork.Web.Pages.Comments.EditModel
@{
    ViewData["Title"] = "Sửa Comment";
}

<h2>Sửa Comment</h2>

<form method="post">
    <input type="hidden" asp-for="Comment.CommentId" />
    <input type="hidden" asp-for="Comment.TaskId" />
    <input type="hidden" asp-for="Comment.UserId" />

    <div class="form-group mb-3">
        <label asp-for="Comment.Content" class="form-label"></label>
        <textarea asp-for="Comment.Content" class="form-control"></textarea>
        <span asp-validation-for="Comment.Content" class="text-danger"></span>
    </div>

    <button type="submit" class="btn btn-success">Cập nhật</button>
    <a asp-page="Index" class="btn btn-secondary ms-2">Hủy</a>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}